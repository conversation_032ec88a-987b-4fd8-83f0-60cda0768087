package com.example.onelinediary.ui.theme

import androidx.compose.ui.graphics.Color

val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

// Custom colors for OneLineDiary - Standard Theme (Green + Yellow Flowers)
val BackgroundGreen = Color(0xFFCCD5AE) // Pastel green background
val ButtonGreen = Color(0xFFA8B882)     // Darker green for buttons (darker tint of background)
val TextBrown = Color(0xFF3E2723)       // Dark brown for text
val AccentGold = Color(0xFFD4A76A)      // Golden accent color (kept for backward compatibility)

// Black and White Theme Colors
val BackgroundWhite = Color(0xFFF5F5F5) // Light background for black and white theme
val ButtonGray = Color(0xFFBDBDBD)      // Darker gray for buttons (darker tint of background)
val TextBlack = Color(0xFF212121)       // Dark text for black and white theme
val AccentGray = Color(0xFF757575)      // Neutral gray accent for black and white theme

// Additional custom background colors - more pastel and soft
val BackgroundBlue = Color(0xFFE8F4FD)      // Very soft pastel blue background
val BackgroundPink = Color(0xFFFDF0F5)      // Very soft pastel pink background
val BackgroundPurple = Color(0xFFF5F0FD)    // Very soft pastel purple background
val BackgroundYellow = Color(0xFFFFFAE8)    // Very soft pastel yellow background
val BackgroundOrange = Color(0xFFFFF5E8)    // Very soft pastel orange background
val BackgroundRed = Color(0xFFFDF0F0)       // Very soft pastel red background
val BackgroundTeal = Color(0xFFE8FDFD)      // Very soft pastel teal background
val BackgroundLavender = Color(0xFFF8F0FD)  // Very soft pastel lavender background

// Darker button colors (softer, more pastel tints of background colors)
val ButtonBlue = Color(0xFFB8D4F0)          // Soft pastel blue for buttons
val ButtonPink = Color(0xFFF0B8D4)          // Soft pastel pink for buttons
val ButtonPurple = Color(0xFFD4B8F0)        // Soft pastel purple for buttons
val ButtonYellow = Color(0xFFF0E8B8)        // Soft pastel yellow for buttons
val ButtonOrange = Color(0xFFF0D4B8)        // Soft pastel orange for buttons
val ButtonRed = Color(0xFFF0B8B8)           // Soft pastel red for buttons
val ButtonTeal = Color(0xFFB8F0F0)          // Soft pastel teal for buttons
val ButtonLavender = Color(0xFFE0B8F0)      // Soft pastel lavender for buttons

// Lighter tint colors for selected/chosen button states (very soft pastel versions)
val LightButtonGreen = Color(0xFFE0E8D0)    // Very light pastel green for selected states
val LightButtonGray = Color(0xFFF0F0F0)     // Very light gray for selected states
val LightButtonBlue = Color(0xFFE0EAFA)     // Very light pastel blue for selected states
val LightButtonPink = Color(0xFFFAE0EA)     // Very light pastel pink for selected states
val LightButtonPurple = Color(0xFFEAE0FA)   // Very light pastel purple for selected states
val LightButtonYellow = Color(0xFFFAF4E0)   // Very light pastel yellow for selected states
val LightButtonOrange = Color(0xFFFAEAE0)   // Very light pastel orange for selected states
val LightButtonRed = Color(0xFFFAE0E0)      // Very light pastel red for selected states
val LightButtonTeal = Color(0xFFE0FAFA)     // Very light pastel teal for selected states
val LightButtonLavender = Color(0xFFF4E0FA) // Very light pastel lavender for selected states

// Soft pastel colors for disabled button states (muted pastel versions)
val SoftPastelGreen = Color(0xFFD8E4C8)     // Soft muted pastel green for disabled states
val SoftPastelGray = Color(0xFFE8E8E8)      // Soft muted pastel gray for disabled states
val SoftPastelBlue = Color(0xFFD0E0F0)      // Soft muted pastel blue for disabled states
val SoftPastelPink = Color(0xFFF0D0E0)      // Soft muted pastel pink for disabled states
val SoftPastelPurple = Color(0xFFE0D0F0)    // Soft muted pastel purple for disabled states
val SoftPastelYellow = Color(0xFFF0ECD0)    // Soft muted pastel yellow for disabled states
val SoftPastelOrange = Color(0xFFF0E0D0)    // Soft muted pastel orange for disabled states
val SoftPastelRed = Color(0xFFF0D0D0)       // Soft muted pastel red for disabled states
val SoftPastelTeal = Color(0xFFD0F0F0)      // Soft muted pastel teal for disabled states
val SoftPastelLavender = Color(0xFFECD0F0)  // Soft muted pastel lavender for disabled states

// Soft pastel complementary colors for disabled button backgrounds (muted pastel complementary colors)
val SoftComplementaryGreen = Color(0xFFE4D8DC)     // Soft pastel reddish-purple (complement of green)
val SoftComplementaryGray = Color(0xFFE0E0E0)      // Soft pastel gray (neutral complement)
val SoftComplementaryBlue = Color(0xFFF0E0D0)      // Soft pastel orange (complement of blue)
val SoftComplementaryPink = Color(0xFFD0F0E0)      // Soft pastel green (complement of pink)
val SoftComplementaryPurple = Color(0xFFF0ECD0)    // Soft pastel yellow (complement of purple)
val SoftComplementaryYellow = Color(0xFFE0D0F0)    // Soft pastel purple (complement of yellow)
val SoftComplementaryOrange = Color(0xFFD0E0F0)    // Soft pastel blue (complement of orange)
val SoftComplementaryRed = Color(0xFFD0F0F0)       // Soft pastel cyan (complement of red)
val SoftComplementaryTeal = Color(0xFFF0D0D0)      // Soft pastel red (complement of teal)
val SoftComplementaryLavender = Color(0xFFE0F0D0)  // Soft pastel lime (complement of lavender)

// Theme enum class to manage theme selection
enum class AppTheme {
    STANDARD,    // Green with yellow flowers (default)
    BLACK_WHITE, // Black and white theme
    BLUE,        // Blue background theme
    PINK,        // Pink background theme
    PURPLE,      // Purple background theme
    YELLOW,      // Yellow background theme
    ORANGE,      // Orange background theme
    RED,         // Red background theme
    TEAL,        // Teal background theme
    LAVENDER     // Lavender background theme
}