package com.example.onelinediary.ui.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.ui.platform.LocalContext

// Dark theme color scheme (not used in our app currently)
private val DarkColorScheme = darkColorScheme(
    primary = Purple80,
    secondary = PurpleGrey80,
    tertiary = Pink80
)

// Standard theme (Green with yellow flowers)
private val StandardColorScheme = lightColorScheme(
    primary = ButtonGreen,
    secondary = ButtonGreen,
    tertiary = ButtonGreen,
    background = BackgroundGreen,
    surface = BackgroundGreen,
    primaryContainer = ButtonGreen,
    onPrimary = TextBrown,
    onSecondary = TextBrown,
    onTertiary = TextBrown,
    onBackground = TextBrown,
    onSurface = TextBrown
)

// Black and White theme
private val BlackWhiteColorScheme = lightColorScheme(
    primary = ButtonGray,
    secondary = ButtonGray,
    tertiary = ButtonGray,
    background = BackgroundWhite,
    surface = BackgroundWhite,
    primaryContainer = ButtonGray,
    onPrimary = TextBlack,
    onSecondary = TextBlack,
    onTertiary = TextBlack,
    onBackground = TextBlack,
    onSurface = TextBlack
)

// Custom color themes
private val BlueColorScheme = lightColorScheme(
    primary = ButtonBlue,
    secondary = ButtonBlue,
    tertiary = ButtonBlue,
    background = BackgroundBlue,
    surface = BackgroundBlue,
    primaryContainer = ButtonBlue,
    onPrimary = TextBlack,
    onSecondary = TextBlack,
    onTertiary = TextBlack,
    onBackground = TextBlack,
    onSurface = TextBlack
)

private val PinkColorScheme = lightColorScheme(
    primary = ButtonPink,
    secondary = ButtonPink,
    tertiary = ButtonPink,
    background = BackgroundPink,
    surface = BackgroundPink,
    primaryContainer = ButtonPink,
    onPrimary = TextBlack,
    onSecondary = TextBlack,
    onTertiary = TextBlack,
    onBackground = TextBlack,
    onSurface = TextBlack
)

private val PurpleColorScheme = lightColorScheme(
    primary = ButtonPurple,
    secondary = ButtonPurple,
    tertiary = ButtonPurple,
    background = BackgroundPurple,
    surface = BackgroundPurple,
    primaryContainer = ButtonPurple,
    onPrimary = TextBlack,
    onSecondary = TextBlack,
    onTertiary = TextBlack,
    onBackground = TextBlack,
    onSurface = TextBlack
)

private val YellowColorScheme = lightColorScheme(
    primary = ButtonYellow,
    secondary = ButtonYellow,
    tertiary = ButtonYellow,
    background = BackgroundYellow,
    surface = BackgroundYellow,
    primaryContainer = ButtonYellow,
    onPrimary = TextBlack,
    onSecondary = TextBlack,
    onTertiary = TextBlack,
    onBackground = TextBlack,
    onSurface = TextBlack
)

private val OrangeColorScheme = lightColorScheme(
    primary = ButtonOrange,
    secondary = ButtonOrange,
    tertiary = ButtonOrange,
    background = BackgroundOrange,
    surface = BackgroundOrange,
    primaryContainer = ButtonOrange,
    onPrimary = TextBlack,
    onSecondary = TextBlack,
    onTertiary = TextBlack,
    onBackground = TextBlack,
    onSurface = TextBlack
)

private val RedColorScheme = lightColorScheme(
    primary = ButtonRed,
    secondary = ButtonRed,
    tertiary = ButtonRed,
    background = BackgroundRed,
    surface = BackgroundRed,
    primaryContainer = ButtonRed,
    onPrimary = TextBlack,
    onSecondary = TextBlack,
    onTertiary = TextBlack,
    onBackground = TextBlack,
    onSurface = TextBlack
)

private val TealColorScheme = lightColorScheme(
    primary = ButtonTeal,
    secondary = ButtonTeal,
    tertiary = ButtonTeal,
    background = BackgroundTeal,
    surface = BackgroundTeal,
    primaryContainer = ButtonTeal,
    onPrimary = TextBlack,
    onSecondary = TextBlack,
    onTertiary = TextBlack,
    onBackground = TextBlack,
    onSurface = TextBlack
)

private val LavenderColorScheme = lightColorScheme(
    primary = ButtonLavender,
    secondary = ButtonLavender,
    tertiary = ButtonLavender,
    background = BackgroundLavender,
    surface = BackgroundLavender,
    primaryContainer = ButtonLavender,
    onPrimary = TextBlack,
    onSecondary = TextBlack,
    onTertiary = TextBlack,
    onBackground = TextBlack,
    onSurface = TextBlack
)

// Composition local to provide the current theme throughout the app
val LocalAppTheme = compositionLocalOf { AppTheme.STANDARD }

@Composable
fun OneLineDiaryTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    // Dynamic color is available on Android 12+
    dynamicColor: Boolean = true,
    appTheme: AppTheme = AppTheme.STANDARD,
    content: @Composable () -> Unit
) {
    // Select color scheme based on the app theme
    val colorScheme = when (appTheme) {
        AppTheme.STANDARD -> StandardColorScheme
        AppTheme.BLACK_WHITE -> BlackWhiteColorScheme
        AppTheme.BLUE -> BlueColorScheme
        AppTheme.PINK -> PinkColorScheme
        AppTheme.PURPLE -> PurpleColorScheme
        AppTheme.YELLOW -> YellowColorScheme
        AppTheme.ORANGE -> OrangeColorScheme
        AppTheme.RED -> RedColorScheme
        AppTheme.TEAL -> TealColorScheme
        AppTheme.LAVENDER -> LavenderColorScheme
    }

    // Provide the current app theme to all composables
    CompositionLocalProvider(LocalAppTheme provides appTheme) {
        MaterialTheme(
            colorScheme = colorScheme,
            typography = Typography,
            content = content
        )
    }
}