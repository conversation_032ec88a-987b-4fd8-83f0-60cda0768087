package com.example.onelinediary.ui.theme

import android.content.Context
import android.content.SharedPreferences
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext

/**
 * Manager class for handling theme preferences in the app
 */
class ThemeManager(private val context: Context) {
    companion object {
        private const val PREFS_NAME = "theme_prefs"
        private const val KEY_THEME = "selected_theme"
        
        // Singleton instance
        @Volatile
        private var INSTANCE: ThemeManager? = null
        
        fun getInstance(context: Context): ThemeManager {
            return INSTANCE ?: synchronized(this) {
                val instance = ThemeManager(context.applicationContext)
                INSTANCE = instance
                instance
            }
        }
    }
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    // Internal mutable state
    private val _currentTheme = mutableStateOf(
        getThemeFromPrefs()
    )
    
    // Public immutable state
    val currentTheme: State<AppTheme> = _currentTheme
    
    /**
     * Get the saved theme from SharedPreferences
     */
    private fun getThemeFromPrefs(): AppTheme {
        val themeName = prefs.getString(KEY_THEME, AppTheme.STANDARD.name)
        return try {
            AppTheme.valueOf(themeName ?: AppTheme.STANDARD.name)
        } catch (e: IllegalArgumentException) {
            // If the stored value is invalid, return the default theme
            AppTheme.STANDARD
        }
    }
    
    /**
     * Set the current theme and save it to SharedPreferences
     */
    fun setTheme(theme: AppTheme) {
        _currentTheme.value = theme
        prefs.edit().putString(KEY_THEME, theme.name).apply()
    }
}

/**
 * Composable function to get the current ThemeManager instance
 */
@Composable
fun rememberThemeManager(): MutableState<AppTheme> {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }
    return remember { mutableStateOf(themeManager.currentTheme.value) }
}
