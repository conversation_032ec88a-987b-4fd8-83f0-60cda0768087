package com.example.onelinediary

import android.content.Context
import android.net.Uri
import android.os.Environment
import android.util.Log
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowLeft
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import coil.compose.rememberAsyncImagePainter
import coil.request.ImageRequest
import com.example.onelinediary.components.AudioPlayerComponent
import com.example.onelinediary.components.ImageViewerComponent
import com.example.onelinediary.components.VideoPlayerComponent
import com.example.onelinediary.components.standardOutlinedButtonColors
import com.example.onelinediary.components.standardButtonModifier
import com.example.onelinediary.components.standardButtonPadding
import com.example.onelinediary.components.standardButtonShape
import com.example.onelinediary.components.backButtonModifier
import com.example.onelinediary.ui.theme.ButtonGreen
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

@Composable
fun HistoryDayScreen(
    date: Calendar,
    onBack: () -> Unit,
    saveDateManager: SaveDateManager
) {
    Log.d("HistoryDayScreen", "Entering HistoryDayScreen with date: ${SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(date.time)}")

    val context = LocalContext.current
    val activity = context as MainActivity

    // Handle system back button press - use direct method
    BackHandler {
        Log.d("HistoryDayScreen", "System back button pressed, using direct method to navigate to history screen")
        activity.forceNavigateToHistoryFromDay()
    }

    // State to keep track of the current date being viewed
    var currentDate by remember { mutableStateOf(date) }

    // Find previous and next dates with content
    val previousDate = remember(currentDate) {
        val prev = saveDateManager.findPreviousDateWithContent(currentDate)
        if (prev != null) {
            Log.d("HistoryDayScreen", "Found previous date with content: ${SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(prev.time)}")
        } else {
            Log.d("HistoryDayScreen", "No previous date with content found")
        }
        prev
    }

    val nextDate = remember(currentDate) {
        val next = saveDateManager.findNextDateWithContent(currentDate)
        if (next != null) {
            Log.d("HistoryDayScreen", "Found next date with content: ${SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(next.time)}")
        } else {
            Log.d("HistoryDayScreen", "No next date with content found")
        }
        next
    }

    val dateFormatter = SimpleDateFormat("EEEE, MMMM d, yyyy", Locale.getDefault())
    val formattedDate = dateFormatter.format(currentDate.time)
    Log.d("HistoryDayScreen", "Formatted date: $formattedDate")

    // Get context for accessing shared preferences
    // Context is already defined at the top level

    // Get content for the current date
    var content by remember { mutableStateOf<Map<ContentType, String>>(emptyMap()) }

    // Debug log for initial render
    LaunchedEffect(Unit) {
        Log.d("HistoryDayScreen", "Initial render of HistoryDayScreen")
    }

    // Load content when the date changes
    LaunchedEffect(currentDate) {
        val dateString = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(currentDate.time)
        Log.d("HistoryDayScreen", "Loading content for date: $dateString")

        // Check if the date is in the saved dates
        val allDates = saveDateManager.getAllSaveDates()
        val isDateSaved = allDates.contains(dateString)
        Log.d("HistoryDayScreen", "Date $dateString is in saved dates: $isDateSaved")

        // Force the date to be saved if it's not already
        if (!isDateSaved) {
            Log.d("HistoryDayScreen", "Date $dateString is not in saved dates, forcing it to be saved")
            // Add a dummy text entry for this date to ensure it's saved
            val diaryPrefs = context.getSharedPreferences("diary_prefs", Context.MODE_PRIVATE)
            diaryPrefs.edit().putString("text_$dateString", "Entry for $dateString").apply()

            // Add this date to the saved dates set
            val updatedDates = allDates.toMutableSet()
            updatedDates.add(dateString)
            saveDateManager.recordSave(ContentType.TEXT)
        }

        // Get content from SaveDateManager
        val newContent = saveDateManager.getContentForDate(currentDate)
        Log.d("HistoryDayScreen", "Content from SaveDateManager: $newContent")

        // If no content is found in SaveDateManager but the date is saved, try to get content from shared preferences
        if (newContent.isEmpty() && isDateSaved) {
            Log.d("HistoryDayScreen", "No content found in SaveDateManager, trying shared preferences")
            val diaryPrefs = context.getSharedPreferences("diary_prefs", Context.MODE_PRIVATE)
            val contentMap = mutableMapOf<ContentType, String>()

            // Check for each content type
            val textContent = diaryPrefs.getString("text_$dateString", null)
            if (textContent != null) {
                contentMap[ContentType.TEXT] = textContent
                Log.d("HistoryDayScreen", "Found TEXT content in shared preferences: $textContent")
            }

            val photoContent = diaryPrefs.getString("photo_$dateString", null)
            if (photoContent != null) {
                contentMap[ContentType.PHOTO] = photoContent
                Log.d("HistoryDayScreen", "Found PHOTO content in shared preferences: $photoContent")
            }

            val videoContent = diaryPrefs.getString("video_$dateString", null)
            if (videoContent != null) {
                contentMap[ContentType.VIDEO] = videoContent
                Log.d("HistoryDayScreen", "Found VIDEO content in shared preferences: $videoContent")
            }

            val audioContent = diaryPrefs.getString("audio_$dateString", null)
            if (audioContent != null) {
                contentMap[ContentType.AUDIO] = audioContent
                Log.d("HistoryDayScreen", "Found AUDIO content in shared preferences: $audioContent")
            }

            val moodContent = diaryPrefs.getString("mood_$dateString", null)
            if (moodContent != null) {
                contentMap[ContentType.MOOD] = moodContent
                Log.d("HistoryDayScreen", "Found MOOD content in shared preferences: $moodContent")
            }

            if (contentMap.isNotEmpty()) {
                content = contentMap
                Log.d("HistoryDayScreen", "Using content from shared preferences: $contentMap")
            } else {
                content = newContent
                Log.d("HistoryDayScreen", "No content found in shared preferences either")
            }
        } else {
            content = newContent
        }

        // Also check for content in the file system directly
        if (content.isEmpty()) {
            Log.d("HistoryDayScreen", "No content found in SaveDateManager or shared preferences, checking file system directly")

            // Check for files in the OnLiDi folder
            val folderName = "OnLiDi"
            val folder = File(Environment.getExternalStoragePublicDirectory(
                Environment.DIRECTORY_DOCUMENTS), folderName)

            if (folder.exists()) {
                val contentMap = mutableMapOf<ContentType, String>()
                val files = folder.listFiles()

                if (files != null) {
                    Log.d("HistoryDayScreen", "Found ${files.size} files in OnLiDi folder")

                    for (file in files) {
                        if (file.name.startsWith(dateString)) {
                            Log.d("HistoryDayScreen", "Found file for date $dateString: ${file.name}")

                            try {
                                val content = file.readText()

                                // Determine content type from filename
                                when {
                                    file.name.contains("_text.txt") -> {
                                        contentMap[ContentType.TEXT] = content
                                        Log.d("HistoryDayScreen", "Found TEXT content in file: $content")
                                    }
                                    file.name.contains("_photo.txt") -> {
                                        contentMap[ContentType.PHOTO] = content
                                        Log.d("HistoryDayScreen", "Found PHOTO content in file: $content")
                                    }
                                    file.name.contains("_video.txt") -> {
                                        contentMap[ContentType.VIDEO] = content
                                        Log.d("HistoryDayScreen", "Found VIDEO content in file: $content")
                                    }
                                    file.name.contains("_audio.txt") -> {
                                        contentMap[ContentType.AUDIO] = content
                                        Log.d("HistoryDayScreen", "Found AUDIO content in file: $content")
                                    }
                                    file.name.contains("_mood.txt") -> {
                                        contentMap[ContentType.MOOD] = content
                                        Log.d("HistoryDayScreen", "Found MOOD content in file: $content")
                                    }
                                    // For old format files (just the date)
                                    file.name == "$dateString.txt" -> {
                                        val fileContent = content
                                        when {
                                            fileContent.startsWith("Photo") -> {
                                                contentMap[ContentType.PHOTO] = fileContent
                                                Log.d("HistoryDayScreen", "Found PHOTO content in old format file: $fileContent")
                                            }
                                            fileContent.startsWith("Video") -> {
                                                contentMap[ContentType.VIDEO] = fileContent
                                                Log.d("HistoryDayScreen", "Found VIDEO content in old format file: $fileContent")
                                            }
                                            fileContent.startsWith("Audio") -> {
                                                contentMap[ContentType.AUDIO] = fileContent
                                                Log.d("HistoryDayScreen", "Found AUDIO content in old format file: $fileContent")
                                            }
                                            fileContent.startsWith("Mood") -> {
                                                contentMap[ContentType.MOOD] = fileContent
                                                Log.d("HistoryDayScreen", "Found MOOD content in old format file: $fileContent")
                                            }
                                            else -> {
                                                contentMap[ContentType.TEXT] = fileContent
                                                Log.d("HistoryDayScreen", "Found TEXT content in old format file: $fileContent")
                                            }
                                        }
                                    }
                                }
                            } catch (e: Exception) {
                                Log.e("HistoryDayScreen", "Error reading file ${file.name}", e)
                            }
                        }
                    }
                }

                if (contentMap.isNotEmpty()) {
                    content = contentMap
                    Log.d("HistoryDayScreen", "Using content from file system: $contentMap")
                }
            } else {
                Log.d("HistoryDayScreen", "OnLiDi folder does not exist")
            }
        }

        Log.d("HistoryDayScreen", "Final content for date ${dateFormatter.format(currentDate.time)}: $content")
    }

    // Get the background color from the theme
    val backgroundColor = MaterialTheme.colorScheme.background

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.Top
    ) {
        // Top navigation row with back button
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Back button - using direct method for reliable navigation
            OutlinedButton(
                onClick = {
                    Log.d("HistoryDayScreen", "Back button clicked, using direct method to navigate to history screen")
                    activity.forceNavigateToHistoryFromDay()
                },
                modifier = backButtonModifier(),
                contentPadding = standardButtonPadding,
                colors = standardOutlinedButtonColors(),
                shape = standardButtonShape
            ) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = "Back to Calendar"
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Date navigation row
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Previous day button
            OutlinedButton(
                onClick = {
                    previousDate?.let { currentDate = it }
                },
                modifier = standardButtonModifier(isDisabled = previousDate == null),
                contentPadding = PaddingValues(4.dp),
                colors = standardOutlinedButtonColors(),
                shape = standardButtonShape,
                enabled = previousDate != null
            ) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.KeyboardArrowLeft,
                    contentDescription = "Previous Day"
                )
            }

            // Date header
            Text(
                text = formattedDate,
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center,
                modifier = Modifier.weight(1f)
            )

            // Next day button
            OutlinedButton(
                onClick = {
                    nextDate?.let { currentDate = it }
                },
                modifier = standardButtonModifier(isDisabled = nextDate == null),
                contentPadding = PaddingValues(4.dp),
                colors = standardOutlinedButtonColors(),
                shape = standardButtonShape,
                enabled = nextDate != null
            ) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                    contentDescription = "Next Day"
                )
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // Content display
        Log.d("HistoryDayScreen", "Rendering content section. Content is ${if (content.isEmpty()) "empty" else "not empty"}")

        // Check specifically for mood content in shared preferences
        val dateString = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(currentDate.time)
        val diaryPrefs = context.getSharedPreferences("diary_prefs", Context.MODE_PRIVATE)
        val moodContent = diaryPrefs.getString("mood_$dateString", null)

        // Create a mutable copy of the content map
        val updatedContent = content.toMutableMap()

        // If mood content exists in shared preferences but not in our content map, add it
        if (moodContent != null && !content.containsKey(ContentType.MOOD)) {
            updatedContent[ContentType.MOOD] = moodContent
            Log.d("HistoryDayScreen", "Added MOOD content from shared preferences: $moodContent")
        }

        // Also check for mood file directly
        if (!updatedContent.containsKey(ContentType.MOOD)) {
            val folderName = "OnLiDi"
            val folder = File(Environment.getExternalStoragePublicDirectory(
                Environment.DIRECTORY_DOCUMENTS), folderName)

            if (folder.exists()) {
                val moodFile = File(folder, "${dateString}_mood.txt")
                if (moodFile.exists()) {
                    try {
                        val moodFileContent = moodFile.readText()
                        updatedContent[ContentType.MOOD] = moodFileContent
                        Log.d("HistoryDayScreen", "Added MOOD content from file: $moodFileContent")
                    } catch (e: Exception) {
                        Log.e("HistoryDayScreen", "Error reading mood file", e)
                    }
                }
            }
        }

        // Force content to have at least one entry
        val displayContent = if (updatedContent.isEmpty()) {
            Log.d("HistoryDayScreen", "Content is empty, creating fallback content")
            mapOf(ContentType.TEXT to "Entry for ${SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(currentDate.time)}")
        } else {
            updatedContent
        }

        // Display each type of content
        Log.d("HistoryDayScreen", "Displaying content items: ${displayContent.keys}")
        displayContent.forEach { (contentType, contentText) ->
            Log.d("HistoryDayScreen", "Rendering content item of type: $contentType with content: $contentText")
            ContentItem(contentType = contentType, content = contentText)
            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

@Composable
fun ContentItem(contentType: ContentType, content: String) {
    // Use the existing context from the parent composable

    androidx.compose.material3.Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        elevation = androidx.compose.material3.CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // Content type header
            Text(
                text = contentType.name,
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.primary
            )

            Spacer(modifier = Modifier.height(8.dp))

            // Content display based on type
            when (contentType) {
                ContentType.PHOTO -> {
                    // Log the content for debugging
                    Log.d("HistoryDayScreen", "Photo content: $content")

                    // Try to extract URI from content with more flexible pattern matching
                    val uriString = extractUriFromContent(content, "content://", "file://")
                    Log.d("HistoryDayScreen", "Extracted URI string: $uriString")

                    if (uriString != null) {
                        // Parse URI outside of composable
                        val uri = try {
                            Uri.parse(uriString)
                        } catch (e: Exception) {
                            Log.e("HistoryDayScreen", "Error parsing URI: $uriString", e)
                            null
                        }

                        if (uri != null) {
                            // Use the ImageViewerComponent for photos
                            Log.d("HistoryDayScreen", "Displaying image with URI: $uri")
                            ImageViewerComponent(
                                imageUri = uri,
                                contentDescription = "Photo"
                            )
                        } else {
                            // Fallback to text display if URI parsing fails
                            Text(
                                text = "Photo: $content",
                                style = MaterialTheme.typography.bodyLarge
                            )
                        }
                    } else {
                        // Try to check if this is a file path
                        val filePath = extractFilePath(content)
                        if (filePath != null) {
                            // Check file existence and create URI outside of composable
                            val fileUri = try {
                                val file = File(filePath)
                                if (file.exists()) {
                                    Uri.fromFile(file)
                                } else {
                                    Log.d("HistoryDayScreen", "File does not exist: $filePath")
                                    null
                                }
                            } catch (e: Exception) {
                                Log.e("HistoryDayScreen", "Error creating URI from file path: $filePath", e)
                                null
                            }

                            if (fileUri != null) {
                                Log.d("HistoryDayScreen", "Displaying image from file path: $fileUri")
                                ImageViewerComponent(
                                    imageUri = fileUri,
                                    contentDescription = "Photo"
                                )
                            } else {
                                Text(
                                    text = "Photo: $content (File not found)",
                                    style = MaterialTheme.typography.bodyLarge
                                )
                            }
                        } else {
                            // Fallback to text display
                            Text(
                                text = "Photo: $content",
                                style = MaterialTheme.typography.bodyLarge
                            )
                        }
                    }
                }
                ContentType.VIDEO -> {
                    // Log the content for debugging
                    Log.d("HistoryDayScreen", "Video content: $content")

                    // Try to extract URI from content with more flexible pattern matching
                    val uriString = extractUriFromContent(content, "content://", "file://")
                    Log.d("HistoryDayScreen", "Extracted video URI string: $uriString")

                    if (uriString != null) {
                        // Parse URI outside of composable
                        val uri = try {
                            Uri.parse(uriString)
                        } catch (e: Exception) {
                            Log.e("HistoryDayScreen", "Error parsing video URI: $uriString", e)
                            null
                        }

                        if (uri != null) {
                            // Use the VideoPlayerComponent for videos
                            Log.d("HistoryDayScreen", "Playing video with URI: $uri")
                            VideoPlayerComponent(videoUri = uri)
                        } else {
                            // Fallback to text display if URI parsing fails
                            Text(
                                text = "Video: $content",
                                style = MaterialTheme.typography.bodyLarge
                            )
                        }
                    } else {
                        // Try to check if this is a file path
                        val filePath = extractFilePath(content)
                        if (filePath != null) {
                            // Check file existence and create URI outside of composable
                            val fileUri = try {
                                val file = File(filePath)
                                if (file.exists()) {
                                    Uri.fromFile(file)
                                } else {
                                    Log.d("HistoryDayScreen", "Video file does not exist: $filePath")
                                    null
                                }
                            } catch (e: Exception) {
                                Log.e("HistoryDayScreen", "Error creating URI from video file path: $filePath", e)
                                null
                            }

                            if (fileUri != null) {
                                Log.d("HistoryDayScreen", "Playing video from file path: $fileUri")
                                VideoPlayerComponent(videoUri = fileUri)
                            } else {
                                Text(
                                    text = "Video: $content (File not found)",
                                    style = MaterialTheme.typography.bodyLarge
                                )
                            }
                        } else {
                            // Fallback to text display
                            Text(
                                text = "Video: $content",
                                style = MaterialTheme.typography.bodyLarge
                            )
                        }
                    }
                }
                ContentType.AUDIO -> {
                    // Log the content for debugging
                    Log.d("HistoryDayScreen", "Audio content: $content")

                    // Try to extract URI from content with more flexible pattern matching
                    val uriString = extractUriFromContent(content, "content://", "file://")
                    Log.d("HistoryDayScreen", "Extracted audio URI string: $uriString")

                    if (uriString != null) {
                        // Parse URI outside of composable
                        val uri = try {
                            Uri.parse(uriString)
                        } catch (e: Exception) {
                            Log.e("HistoryDayScreen", "Error parsing audio URI: $uriString", e)
                            null
                        }

                        if (uri != null) {
                            // Use the AudioPlayerComponent for audio
                            Log.d("HistoryDayScreen", "Playing audio with URI: $uri")
                            AudioPlayerComponent(audioUri = uri)
                        } else {
                            // Fallback to text display if URI parsing fails
                            Text(
                                text = "Audio: $content",
                                style = MaterialTheme.typography.bodyLarge
                            )
                        }
                    } else {
                        // Try to check if this is a file path
                        val filePath = extractFilePath(content)
                        if (filePath != null) {
                            // Check file existence and create URI outside of composable
                            val fileUri = try {
                                val file = File(filePath)
                                if (file.exists()) {
                                    Uri.fromFile(file)
                                } else {
                                    Log.d("HistoryDayScreen", "Audio file does not exist: $filePath")
                                    null
                                }
                            } catch (e: Exception) {
                                Log.e("HistoryDayScreen", "Error creating URI from audio file path: $filePath", e)
                                null
                            }

                            if (fileUri != null) {
                                Log.d("HistoryDayScreen", "Playing audio from file path: $fileUri")
                                AudioPlayerComponent(audioUri = fileUri)
                            } else {
                                Text(
                                    text = "Audio: $content (File not found)",
                                    style = MaterialTheme.typography.bodyLarge
                                )
                            }
                        } else {
                            // Fallback to text display
                            Text(
                                text = "Audio: $content",
                                style = MaterialTheme.typography.bodyLarge
                            )
                        }
                    }
                }
                ContentType.TEXT -> {
                    // Log the text content for debugging
                    Log.d("HistoryDayScreen", "Text content: $content")

                    // Display the text content in a nicely formatted way
                    androidx.compose.material3.Card(
                        modifier = Modifier
                            .fillMaxWidth(),
                        colors = androidx.compose.material3.CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surfaceVariant
                        )
                    ) {
                        Text(
                            text = content,
                            style = MaterialTheme.typography.bodyLarge,
                            modifier = Modifier.padding(16.dp)
                        )
                    }
                }
                ContentType.MOOD -> {
                    // Log the mood content for debugging
                    Log.d("HistoryDayScreen", "Mood content: $content")

                    // Extract the mood value from the content string
                    val moodValue = when {
                        content.contains("Very Sad") -> "very_sad"
                        content.contains("Sad") -> "sad"
                        content.contains("Neutral") -> "neutral"
                        content.contains("Happy") -> "happy"
                        content.contains("Very Happy") -> "very_happy"
                        else -> null
                    }

                    // Define mood color
                    val moodColor = when (moodValue) {
                        "very_sad" -> Color(0xFFE57373) // Light Red
                        "sad" -> Color(0xFFFFB74D)      // Light Orange
                        "neutral" -> Color(0xFFFFEE58)   // Light Yellow
                        "happy" -> Color(0xFFAED581)     // Light Green
                        "very_happy" -> Color(0xFF81C784) // Green
                        else -> MaterialTheme.colorScheme.primary
                    }

                    // Get emoji for the mood
                    val moodEmoji = when (moodValue) {
                        "very_sad" -> "😢"
                        "sad" -> "😔"
                        "neutral" -> "😐"
                        "happy" -> "😊"
                        "very_happy" -> "😄"
                        else -> "🙂"
                    }

                    // Get mood text
                    val moodText = when (moodValue) {
                        "very_sad" -> "Very Sad"
                        "sad" -> "Sad"
                        "neutral" -> "Neutral"
                        "happy" -> "Happy"
                        "very_happy" -> "Very Happy"
                        else -> "Unknown"
                    }

                    // Display the mood content in a nicely formatted way with an icon
                    androidx.compose.material3.Card(
                        modifier = Modifier
                            .fillMaxWidth(),
                        colors = androidx.compose.material3.CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surfaceVariant
                        )
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            // Mood title
                            Text(
                                text = "Today's Mood",
                                style = MaterialTheme.typography.titleMedium,
                                color = MaterialTheme.colorScheme.primary
                            )

                            Spacer(modifier = Modifier.height(16.dp))

                            // Mood indicator with emoji
                            Box(
                                modifier = Modifier
                                    .size(64.dp)
                                    .background(color = moodColor, shape = CircleShape)
                                    .border(
                                        width = 2.dp,
                                        color = moodColor.copy(alpha = 0.7f),
                                        shape = CircleShape
                                    ),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = moodEmoji,
                                    style = MaterialTheme.typography.headlineLarge
                                )
                            }

                            Spacer(modifier = Modifier.height(8.dp))

                            // Mood text
                            Text(
                                text = moodText,
                                style = MaterialTheme.typography.bodyLarge,
                                fontWeight = FontWeight.Bold
                            )
                        }
                    }
                }
            }
        }
    }
}

// Helper function to extract URI from content string
private fun extractUriFromContent(content: String, vararg prefixes: String): String? {
    for (prefix in prefixes) {
        if (content.contains(prefix)) {
            val startIndex = content.indexOf(prefix)
            // Try to find the end of the URI (look for closing parenthesis, quote, or whitespace)
            val endMarkers = listOf(")", "\"", "'", " ", "\n")
            val endIndices = endMarkers.mapNotNull {
                val idx = content.indexOf(it, startIndex)
                if (idx > 0) idx else null
            }

            val endIndex = if (endIndices.isNotEmpty()) endIndices.min() else content.length
            return content.substring(startIndex, endIndex)
        }
    }
    return null
}

// Helper function to extract file path from content string
private fun extractFilePath(content: String): String? {
    // Look for common file path patterns
    val patterns = listOf(
        "/storage/",
        "/sdcard/",
        "/data/",
        "/mnt/",
        "/external_files/",
        "/external/"
    )

    for (pattern in patterns) {
        if (content.contains(pattern)) {
            val startIndex = content.indexOf(pattern)
            // Try to find the end of the path (look for closing parenthesis, quote, or whitespace)
            val endMarkers = listOf(")", "\"", "'", " ", "\n")
            val endIndices = endMarkers.mapNotNull {
                val idx = content.indexOf(it, startIndex)
                if (idx > 0) idx else null
            }

            val endIndex = if (endIndices.isNotEmpty()) endIndices.min() else content.length
            return content.substring(startIndex, endIndex)
        }
    }
    return null
}
